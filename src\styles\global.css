@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Theme Support */
:root {
  /* Light theme colors */
  --color-bg-primary: theme('colors.background.light');
  --color-bg-secondary: theme('colors.background.light-secondary');
  --color-text-primary: theme('colors.text.light');
  --color-text-secondary: theme('colors.text.light-secondary');
  --color-border: theme('colors.secondary.200');
  --color-shadow: rgba(0, 0, 0, 0.1);

  /* Enhanced glassmorphism variables - 2025 sophistication */
  --glass-bg: rgba(254, 254, 254, 0.15);
  --glass-border: rgba(158, 122, 104, 0.12);
  --glass-shadow: 0 12px 40px -8px rgba(45, 42, 35, 0.08), 0 4px 16px -4px rgba(158, 122, 104, 0.12);

  /* Theme transition variables */
  --theme-transition-duration: 0.3s;
  --theme-transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

.dark {
  /* Dark theme colors (following 2024 best practices - avoiding pure black) */
  --color-bg-primary: theme('colors.background.dark');
  --color-bg-secondary: theme('colors.background.dark-secondary');
  --color-text-primary: theme('colors.text.dark');
  --color-text-secondary: theme('colors.text.dark-secondary');
  --color-border: theme('colors.secondary.600');
  --color-shadow: rgba(0, 0, 0, 0.4);

  /* Dark mode glassmorphism - sophisticated warm tones */
  --glass-bg: rgba(26, 25, 22, 0.45);
  --glass-border: rgba(232, 230, 225, 0.08);
  --glass-shadow: 0 12px 40px -8px rgba(0, 0, 0, 0.35), 0 4px 16px -4px rgba(158, 122, 104, 0.15);
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  transition: background-color var(--theme-transition-duration) var(--theme-transition-easing);
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--color-text-primary);
  line-height: 1.6;
  background-color: var(--color-bg-primary);
  transition: 
    background-color var(--theme-transition-duration) var(--theme-transition-easing),
    color var(--theme-transition-duration) var(--theme-transition-easing);

  /* Optimize font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-variant-ligatures: common-ligatures;
}

/* Main content area - flex-grow handled in Layout.astro */

/* Improved focus styles for accessibility */
*:focus {
  outline: 2px solid theme('colors.primary.500');
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid theme('colors.primary.500');
  outline-offset: 2px;
}

/* Performance optimizations */
img {
  height: auto;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

img[loading="lazy"] {
  transition: opacity 0.3s ease-in-out;
}

/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Component Styles */
@layer components {
  /* Modern glassmorphism card */
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: theme('borderRadius.2xl');
  }

  /* Modern 2025 sophisticated button styles */
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-2xl;
    background: linear-gradient(135deg, theme('colors.primary.600') 0%, theme('colors.primary.500') 50%, theme('colors.accent.500') 100%);
    @apply text-white relative overflow-hidden;
    box-shadow: 
      0 4px 20px -4px rgba(158, 122, 104, 0.4),
      0 8px 32px -8px rgba(158, 122, 104, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    @apply transition-all duration-500 ease-out;
    @apply hover:-translate-y-1 hover:scale-[1.02];
    @apply focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-background-light dark:focus:ring-offset-background-dark;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }

  .btn-primary:hover {
    box-shadow: 
      0 8px 32px -4px rgba(158, 122, 104, 0.5),
      0 16px 48px -8px rgba(158, 122, 104, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .btn-primary::before {
    content: '';
    @apply absolute inset-0 rounded-2xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
    @apply opacity-0 transition-opacity duration-300;
  }

  .btn-primary:hover::before {
    @apply opacity-100;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-2xl;
    @apply bg-white/70 dark:bg-secondary-800/70 backdrop-blur-sm;
    @apply text-text-light dark:text-text-dark relative overflow-hidden;
    border: 1px solid rgba(158, 122, 104, 0.15);
    box-shadow: 
      0 2px 12px -2px rgba(158, 122, 104, 0.1),
      0 4px 24px -4px rgba(158, 122, 104, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    @apply transition-all duration-500 ease-out;
    @apply hover:-translate-y-1 hover:scale-[1.02];
    @apply hover:bg-white/90 dark:hover:bg-secondary-700/80;
    @apply hover:border-primary-300/30 dark:hover:border-primary-500/30;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-background-light dark:focus:ring-offset-background-dark;
  }

  .btn-secondary:hover {
    box-shadow: 
      0 4px 20px -2px rgba(158, 122, 104, 0.15),
      0 8px 32px -4px rgba(158, 122, 104, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl;
    color: var(--color-text-secondary);
    @apply transition-all duration-300;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-ghost:hover {
    background-color: var(--color-bg-secondary);
  }

  /* Modern 2025 sophisticated card styles */
  .card {
    @apply bg-white/80 dark:bg-secondary-800/60 backdrop-blur-sm;
    border: 1px solid rgba(158, 122, 104, 0.1);
    @apply rounded-3xl relative overflow-hidden;
    box-shadow: 
      0 2px 16px -4px rgba(158, 122, 104, 0.08),
      0 4px 32px -8px rgba(45, 42, 35, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    @apply transition-all duration-500 ease-out hover:-translate-y-2;
  }

  .card:hover {
    box-shadow: 
      0 8px 32px -4px rgba(158, 122, 104, 0.12),
      0 16px 64px -8px rgba(45, 42, 35, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
    @apply bg-white/95 dark:bg-secondary-800/80;
    border-color: rgba(158, 122, 104, 0.2);
  }

  .card-interactive {
    @apply card cursor-pointer;
    @apply hover:scale-[1.02];
    @apply hover:border-primary-300/40 dark:hover:border-primary-500/40;
  }

  .card-interactive::before {
    content: '';
    @apply absolute inset-0 rounded-3xl;
    background: linear-gradient(135deg, rgba(158, 122, 104, 0.02) 0%, transparent 50%, rgba(241, 191, 79, 0.01) 100%);
    @apply opacity-0 transition-opacity duration-500;
  }

  .card-interactive:hover::before {
    @apply opacity-100;
  }

  /* Modern 2025 typography improvements */
  .text-gradient {
    background: linear-gradient(135deg, 
      theme('colors.primary.700') 0%, 
      theme('colors.primary.600') 25%, 
      theme('colors.primary.500') 50%, 
      theme('colors.accent.600') 75%, 
      theme('colors.accent.500') 100%);
    @apply bg-clip-text text-transparent;
  }

  .heading-xl {
    @apply text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold font-heading leading-tight tracking-tight;
    @apply text-gradient;
    text-shadow: 0 2px 4px rgba(45, 42, 35, 0.1);
  }

  .heading-lg {
    @apply text-3xl sm:text-4xl lg:text-5xl font-bold font-heading leading-tight tracking-tight;
    @apply text-text-light dark:text-text-dark;
    text-shadow: 0 1px 2px rgba(45, 42, 35, 0.1);
  }

  .heading-md {
    @apply text-2xl sm:text-3xl font-semibold font-heading leading-tight tracking-tight;
    @apply text-text-light dark:text-text-dark;
  }

  .heading-sm {
    @apply text-xl sm:text-2xl font-semibold font-heading leading-tight tracking-tight;
    @apply text-text-light dark:text-text-dark;
  }

  /* Navigation styles */
  .nav-link {
    @apply relative px-3 py-2 text-sm font-medium transition-all duration-300;
    @apply text-secondary-600 hover:text-primary-600;
    @apply dark:text-secondary-300 dark:hover:text-primary-400;
  }

  .nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-full h-0.5;
    @apply bg-gradient-to-r from-primary-500 to-accent-500;
    @apply transform scale-x-0 transition-transform duration-300 origin-right;
  }

  .nav-link:hover::after,
  .nav-link.active::after {
    @apply scale-x-100 origin-left;
  }

  /* Section spacing */
  .section {
    @apply py-16 lg:py-24;
  }

  .section-sm {
    @apply py-12 lg:py-16;
  }

  /* Container improvements */
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
}

/* Utility Classes */
@layer utilities {
  /* Theme transition utilities */
  .theme-transition {
    transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  /* Glassmorphism utilities */
  .backdrop-blur-glass {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  /* Animation utilities */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
  }

  /* Text selection */
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Modern shadows */
  .shadow-glow-primary {
    box-shadow: 0 0 20px theme('colors.primary.500/15');
  }

  .shadow-glow-accent {
    box-shadow: 0 0 20px theme('colors.accent.500/15');
  }

  /* Enhanced theme transition utilities */
  .theme-transition-enhanced {
    transition:
      background-color var(--theme-transition-duration) var(--theme-transition-easing),
      color var(--theme-transition-duration) var(--theme-transition-easing),
      border-color var(--theme-transition-duration) var(--theme-transition-easing),
      box-shadow var(--theme-transition-duration) var(--theme-transition-easing),
      opacity var(--theme-transition-duration) var(--theme-transition-easing);
  }

  /* Page-wide theme transition effect */
  .theme-transition-page {
    transition: all var(--theme-transition-duration) var(--theme-transition-easing);
  }
}

/* Enhanced Global Theme Transitions */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow;
  transition-duration: var(--theme-transition-duration);
  transition-timing-function: var(--theme-transition-easing);
}

/* Page-wide theme transition effects */
body {
  position: relative;
  overflow-x: hidden;
}

/* Modern theme transition overlay styles */
.theme-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 9999;
  transition: opacity 0.2s ease-out;
}

/* Fade blur transition for desktop */
.theme-fade-blur {
  backdrop-filter: blur(0px);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Slide overlay for mobile */
.theme-slide-overlay {
  transition: top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Curtain transition for desktop - elegant and professional */
.theme-curtain-left,
.theme-curtain-right {
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
}

.theme-curtain-left {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-curtain-right {
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

/* Alternative gradient sweep animation */
.theme-transition-sweep {
  position: fixed;
  top: 0;
  left: -100%;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 9998;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(59, 130, 246, 0.1) 25%,
    rgba(59, 130, 246, 0.2) 50%,
    rgba(59, 130, 246, 0.1) 75%,
    transparent 100%
  );
  transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-transition-sweep.active {
  left: 100%;
}

/* Ripple effect for page transitions */
@keyframes theme-ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.theme-ripple-effect {
  position: fixed;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.3) 0%,
    rgba(59, 130, 246, 0.1) 50%,
    transparent 100%
  );
  pointer-events: none;
  z-index: 9997;
  animation: theme-ripple 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Enhanced body transitions for smoother theme switching */
body.theme-transitioning {
  transition: background-color 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Staggered element animations during theme transition */
.theme-transition-stagger > * {
  transition-delay: calc(var(--stagger-delay, 0) * 50ms);
}

/* Fade transition for sensitive elements */
.theme-transition-fade {
  transition: opacity 0.3s ease-out;
}

.theme-transition-fade.transitioning {
  opacity: 0.7;
}

/* Resources Page Specific Styles */
@layer components {
  /* Line clamp utility for resource descriptions */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Enhanced resource card animations */
  .resource-card {
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .resource-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  /* Fade in up animation for filtered resources */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-fade-in-up {
    animation: fadeInUp 0.5s ease-out forwards;
  }
  
  /* Enhanced filter button styles */
  .filter-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }
  
  .filter-btn:hover::before {
    left: 100%;
  }
  
  /* Search input enhancements */
  .search-input {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .search-input:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  /* Toast notification styles */
  .toast {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }
  
  /* Bookmark button animation */
  .bookmark-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .bookmark-btn:hover {
    transform: scale(1.1);
  }
  
  .bookmark-btn.bookmarked {
    animation: bookmarkPulse 0.6s ease-out;
  }
  
  @keyframes bookmarkPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
  }
  
  /* Share button ripple effect */
  .share-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .share-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }
  
  .share-btn:active::after {
    width: 200px;
    height: 200px;
  }
  
  /* Enhanced RSS feed preview */
  .feed-preview {
    position: relative;
    overflow: hidden;
  }
  
  .feed-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
  }
  
  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }
  
  /* View toggle button animation */
  .view-toggle {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .view-toggle:hover {
    transform: rotate(180deg);
  }
  
  /* Staggered animation for resource cards */
  .resources-grid .resource-card {
    animation-delay: calc(var(--card-index, 0) * 100ms);
  }
  
  /* Enhanced glassmorphism for resource page */
  .glass-card-enhanced {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
      0 8px 32px 0 rgba(31, 38, 135, 0.37),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .dark .glass-card-enhanced {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 8px 32px 0 rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Custom prose styling for portfolio projects */
  .prose-primary {
    @apply prose-blue;
  }

  .prose-primary a {
    @apply text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300;
  }

  .prose-primary strong {
    @apply text-secondary-800 dark:text-secondary-200;
  }

  .prose-primary code {
    @apply text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20;
  }

  .prose-primary blockquote {
    @apply border-l-primary-500 text-secondary-700 dark:text-secondary-300;
  }

  .prose-primary h1,
  .prose-primary h2,
  .prose-primary h3,
  .prose-primary h4,
  .prose-primary h5,
  .prose-primary h6 {
    @apply text-secondary-800 dark:text-secondary-200;
  }

  .prose-primary p {
    @apply text-secondary-600 dark:text-secondary-400;
  }

  .prose-primary ul,
  .prose-primary ol {
    @apply text-secondary-600 dark:text-secondary-400;
  }

  .prose-primary li {
    @apply marker:text-secondary-400 dark:marker:text-secondary-500;
  }
}

/* Accessibility: Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  :root {
    --theme-transition-duration: 0.01s;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01s !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01s !important;
    scroll-behavior: auto !important;
  }
  
  .animate-fade-in-up {
    animation: none;
  }
  
  .filter-btn::before,
  .share-btn::after,
  .feed-preview::before {
    display: none;
  }
}