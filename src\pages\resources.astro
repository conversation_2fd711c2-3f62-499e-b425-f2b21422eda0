---
import Layout from '../layouts/Layout.astro';
import { getCollection } from 'astro:content';

const resources = await getCollection('resources');
const categories = [...new Set(resources.map(resource => resource.data.category))];
const allTags = [...new Set(resources.flatMap(resource => resource.data.tags))];
---

<Layout title="Resources | Nob Hokleng | Software Developer & System Architect">
  <section class="resources pt-32 pb-24 bg-gradient-to-br from-slate-50 via-white to-slate-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950">
    <div class="container mx-auto px-5 max-w-7xl">
      <div class="text-center mb-16">
        <h1 class="text-5xl font-bold mb-4 font-heading bg-gradient-to-r from-slate-900 via-slate-700 to-slate-600 dark:from-slate-100 dark:via-slate-300 dark:to-slate-400 bg-clip-text text-transparent">
          Resources & Knowledge Hub
        </h1>
        <p class="text-xl text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
          Curated collection of tools, articles, and resources for developers and tech enthusiasts
        </p>
      </div>
      
      <!-- Search and Filter Controls -->
      <div class="mb-12 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border border-slate-200/50 dark:border-slate-700/50 p-6 rounded-2xl shadow-sm">
        <div class="flex flex-col lg:flex-row gap-6 items-center">
          <!-- Search Bar -->
          <div class="flex-1 relative">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <input 
              type="text" 
              id="resource-search" 
              placeholder="Search resources, tools, articles..." 
              class="w-full pl-12 pr-4 py-3 bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300 text-slate-900 dark:text-slate-100 placeholder-slate-500"
              aria-label="Search resources"
            >
          </div>
          
          <!-- Sort Options -->
          <div class="flex gap-3">
            <select id="sort-select" class="px-4 py-3 bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-slate-900 dark:text-slate-100" aria-label="Sort resources">
              <option value="title">Sort by Title</option>
              <option value="category">Sort by Category</option>
              <option value="recent">Recently Added</option>
            </select>
            
            <button id="view-toggle" class="px-4 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-xl transition-all duration-300 flex items-center gap-2 shadow-sm hover:shadow-md" aria-label="Toggle view mode">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
              </svg>
              Grid
            </button>
          </div>
        </div>
        
        <!-- Category and Tag Filters -->
        <div class="mt-6 flex flex-wrap gap-3">
          <div class="flex flex-wrap gap-2">
            <button class="filter-btn active px-4 py-2 bg-indigo-600 text-white rounded-full text-sm font-medium transition-all duration-300 hover:bg-indigo-700 shadow-sm" data-filter="all">
              All Resources
            </button>
            {categories.map(category => (
              <button class="filter-btn px-4 py-2 bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-full text-sm font-medium transition-all duration-300 hover:bg-slate-200 dark:hover:bg-slate-700 border border-slate-200 dark:border-slate-700" data-filter={category.toLowerCase()}>
                {category}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
        <!-- RSS Feed Section -->
        <div class="xl:col-span-1">
          <div class="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border border-slate-200/50 dark:border-slate-700/50 p-6 rounded-2xl shadow-sm sticky top-8">
            <div class="flex items-center gap-3 mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3.429 2.571A1.429 1.429 0 002 4v12a1.429 1.429 0 001.429 1.429h12A1.429 1.429 0 0017 16V4a1.429 1.429 0 00-1.429-1.429H3.43zM4 6.286C4 5.576 4.576 5 5.286 5h.857C6.424 5 7 5.576 7 6.286v.857C7 7.424 6.424 8 5.714 8h-.857C4.576 8 4 7.424 4 6.857v-.571zm0 4.571c0-.71.576-1.286 1.286-1.286h.857c.71 0 1.286.576 1.286 1.286v.857c0 .71-.576 1.286-1.286 1.286h-.857C4.576 12.571 4 11.995 4 11.286v-.857z"/>
                </svg>
              </div>
              <h2 class="text-xl font-bold text-slate-800 dark:text-slate-200">RSS Feed</h2>
            </div>
            
            <p class="text-slate-600 dark:text-slate-400 mb-6 text-sm leading-relaxed">
              Stay updated with my latest articles, tutorials, and tech insights
            </p>
            
            <div class="space-y-3 mb-6">
              <button type="button" class="w-full flex items-center justify-center gap-3 px-4 py-3 border-2 border-indigo-500 text-indigo-600 dark:text-indigo-400 rounded-xl font-medium hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-all duration-300" onclick="showRSSOptions()" aria-label="Subscribe to RSS feed">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3.429 2.571A1.429 1.429 0 002 4v12a1.429 1.429 0 001.429 1.429h12A1.429 1.429 0 0017 16V4a1.429 1.429 0 00-1.429-1.429H3.43z"/>
                </svg>
                Subscribe to RSS
              </button>
              <button type="button" class="w-full flex items-center justify-center gap-3 px-4 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5" onclick="copyRSSUrl()" aria-label="Copy RSS feed URL">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                </svg>
                Copy RSS URL
              </button>
            </div>
            
            <div class="recent-posts">
              <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-4">Recent Posts</h3>
              <div id="rss-feed-preview" class="bg-slate-50 dark:bg-slate-800/50 p-4 rounded-xl">
                <div class="animate-pulse space-y-3">
                  <div class="h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4"></div>
                  <div class="h-3 bg-slate-200 dark:bg-slate-700 rounded w-1/2"></div>
                  <div class="h-4 bg-slate-200 dark:bg-slate-700 rounded w-5/6"></div>
                  <div class="h-3 bg-slate-200 dark:bg-slate-700 rounded w-2/3"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Resources Grid -->
        <div class="xl:col-span-3">
          <div id="resources-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {resources.map((resource, index) => (
              <div class="resource-card bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border border-slate-200/50 dark:border-slate-700/50 p-6 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1" data-category={resource.data.category.toLowerCase()} data-tags={resource.data.tags.join(',').toLowerCase()} data-title={resource.data.title.toLowerCase()}>
                <div class="flex items-start justify-between mb-4">
                  <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-slate-600 to-slate-800 dark:from-slate-400 dark:to-slate-600 rounded-xl flex items-center justify-center text-white font-bold text-sm">
                      {resource.data.title.charAt(0).toUpperCase()}
                    </div>
                    <div class="flex-1">
                      <span class="inline-block px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 text-xs rounded-full font-medium mb-1">
                        {resource.data.category}
                      </span>
                    </div>
                  </div>
                  <div class="flex gap-2">
                    <button class="bookmark-btn p-2 text-slate-400 hover:text-amber-500 transition-colors duration-300" aria-label="Bookmark resource" data-url={resource.data.url}>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                      </svg>
                    </button>
                    <button class="share-btn p-2 text-slate-400 hover:text-indigo-500 transition-colors duration-300" aria-label="Share resource" data-url={resource.data.url} data-title={resource.data.title}>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                      </svg>
                    </button>
                  </div>
                </div>
                
                <h3 class="font-bold text-lg text-slate-800 dark:text-slate-200 mb-3 leading-tight">
                  <a href={resource.data.url} target="_blank" rel="noopener" class="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-300">
                    {resource.data.title}
                  </a>
                </h3>
                
                <p class="text-slate-600 dark:text-slate-400 text-sm mb-4 leading-relaxed line-clamp-3">
                  {resource.data.description}
                </p>
                
                <div class="flex flex-wrap gap-2 mb-4">
                  {resource.data.tags.slice(0, 3).map(tag => (
                    <span class="px-2 py-1 bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 text-xs rounded-full font-medium border border-slate-200 dark:border-slate-700">
                      {tag}
                    </span>
                  ))}
                  {resource.data.tags.length > 3 && (
                    <span class="px-2 py-1 bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 text-xs rounded-full border border-slate-200 dark:border-slate-700">
                      +{resource.data.tags.length - 3} more
                    </span>
                  )}
                </div>
                
                <div class="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700">
                  <a href={resource.data.url} target="_blank" rel="noopener" class="inline-flex items-center gap-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 font-medium text-sm transition-colors duration-300">
                    Visit Resource
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                    </svg>
                  </a>
                  <div class="flex items-center gap-1 text-slate-400 text-xs">
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                    </svg>
                    <span>Recently added</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <!-- No Results Message -->
          <div id="no-results" class="hidden text-center py-12">
            <div class="w-16 h-16 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">No resources found</h3>
            <p class="text-slate-600 dark:text-slate-400">Try adjusting your search or filter criteria</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- RSS Modal -->
  <div id="rss-modal" class="rss-modal fixed inset-0 bg-slate-900/50 backdrop-blur-sm z-50 hidden" role="dialog" aria-labelledby="rss-modal-title" aria-hidden="true">
    <div class="rss-modal-content bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-2xl max-w-2xl mx-auto mt-20 p-8 m-4 shadow-xl">
      <div class="rss-modal-header flex justify-between items-center mb-6">
        <h4 id="rss-modal-title" class="text-2xl font-bold text-slate-800 dark:text-slate-200 font-heading">Subscribe to My RSS Feed</h4>
        <button type="button" class="close-modal text-2xl text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200" onclick="closeRSSModal()" aria-label="Close RSS subscription modal">&times;</button>
      </div>
      <div class="rss-modal-body">
        <p class="text-slate-600 dark:text-slate-400 mb-6">Choose your preferred way to subscribe:</p>
        <div class="rss-options space-y-6">
          <div class="rss-option">
            <h5 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-3">📱 Mobile Apps</h5>
            <div class="rss-links flex flex-wrap gap-3">
              <a href="feedly://i/subscription/feed/https://nobhokleng.dev/rss.xml" class="rss-link flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                </svg>
                Feedly
              </a>
              <a href="inoreader://add_feed/https://nobhokleng.dev/rss.xml" class="rss-link flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                </svg>
                Inoreader
              </a>
            </div>
          </div>
          
          <div class="rss-option">
            <h5 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-3">🌐 Web Readers</h5>
            <div class="rss-links flex flex-wrap gap-3">
              <a href="https://feedly.com/i/subscription/feed/https://nobhokleng.dev/rss.xml" target="_blank" rel="noopener" class="rss-link flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                </svg>
                Feedly Web
              </a>
              <a href="https://www.inoreader.com/feed/https://nobhokleng.dev/rss.xml" target="_blank" rel="noopener" class="rss-link flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                </svg>
                Inoreader Web
              </a>
            </div>
          </div>
          
          <div class="rss-url-display">
            <label class="block font-semibold text-slate-800 dark:text-slate-200 mb-2">RSS Feed URL:</label>
            <div class="url-input-group flex">
              <input type="text" id="rss-url-input" value="https://nobhokleng.dev/rss.xml" readonly class="flex-1 p-3 border border-slate-300 dark:border-slate-600 rounded-l-lg bg-slate-50 dark:bg-slate-800 text-slate-900 dark:text-slate-100">
              <button type="button" onclick="copyFromInput()" class="copy-btn px-4 py-3 bg-indigo-600 text-white rounded-r-lg hover:bg-indigo-700 transition-colors" aria-label="Copy RSS URL from input field">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script is:inline>
  // Enhanced Resources Page Functionality
  class ResourcesManager {
    constructor() {
      this.resources = [];
      this.filteredResources = [];
      this.currentFilter = 'all';
      this.currentSort = 'title';
      this.searchQuery = '';
      this.isGridView = true;
      
      this.init();
    }
    
    init() {
      this.cacheResources();
      this.bindEvents();
      this.loadRSSFeed();
      this.initializeBookmarks();
    }
    
    cacheResources() {
      const resourceCards = document.querySelectorAll('.resource-card');
      this.resources = Array.from(resourceCards).map(card => ({
        element: card,
        title: card.dataset.title || '',
        category: card.dataset.category || '',
        tags: card.dataset.tags ? card.dataset.tags.split(',') : [],
        url: card.querySelector('a[href]')?.href || ''
      }));
      this.filteredResources = [...this.resources];
    }
    
    bindEvents() {
      // Search functionality
      const searchInput = document.getElementById('resource-search');
      if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
          clearTimeout(searchTimeout);
          searchTimeout = setTimeout(() => {
            this.searchQuery = e.target.value.toLowerCase();
            this.filterAndSort();
          }, 300);
        });
      }
      
      // Filter buttons
      document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          this.setActiveFilter(e.target);
          this.currentFilter = e.target.dataset.filter;
          this.filterAndSort();
        });
      });
      
      // Sort dropdown
      const sortSelect = document.getElementById('sort-select');
      if (sortSelect) {
        sortSelect.addEventListener('change', (e) => {
          this.currentSort = e.target.value;
          this.filterAndSort();
        });
      }
      
      // View toggle
      const viewToggle = document.getElementById('view-toggle');
      if (viewToggle) {
        viewToggle.addEventListener('click', () => {
          this.toggleView();
        });
      }
      
      // Bookmark buttons
      document.querySelectorAll('.bookmark-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          this.toggleBookmark(btn);
        });
      });
      
      // Share buttons
      document.querySelectorAll('.share-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          this.shareResource(btn);
        });
      });
    }
    
    setActiveFilter(activeBtn) {
      document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active', 'bg-indigo-600', 'text-white');
        btn.classList.add('bg-slate-100', 'dark:bg-slate-800', 'text-slate-700', 'dark:text-slate-300');
      });
      
      activeBtn.classList.add('active', 'bg-indigo-600', 'text-white');
      activeBtn.classList.remove('bg-slate-100', 'dark:bg-slate-800', 'text-slate-700', 'dark:text-slate-300');
    }
    
    filterAndSort() {
      // Filter resources
      this.filteredResources = this.resources.filter(resource => {
        const matchesSearch = !this.searchQuery || 
          resource.title.includes(this.searchQuery) ||
          resource.category.includes(this.searchQuery) ||
          resource.tags.some(tag => tag.includes(this.searchQuery));
          
        const matchesFilter = this.currentFilter === 'all' || 
          resource.category === this.currentFilter;
          
        return matchesSearch && matchesFilter;
      });
      
      // Sort resources
      this.filteredResources.sort((a, b) => {
        switch (this.currentSort) {
          case 'title':
            return a.title.localeCompare(b.title);
          case 'category':
            return a.category.localeCompare(b.category);
          case 'recent':
            return 0;
          default:
            return 0;
        }
      });
      
      this.renderResources();
    }
    
    renderResources() {
      const container = document.getElementById('resources-container');
      const noResults = document.getElementById('no-results');
      
      if (!container) return;
      
      // Hide all resources first
      this.resources.forEach(resource => {
        resource.element.style.display = 'none';
      });
      
      if (this.filteredResources.length === 0) {
        noResults?.classList.remove('hidden');
      } else {
        noResults?.classList.add('hidden');
        
        // Show filtered resources
        this.filteredResources.forEach((resource, index) => {
          resource.element.style.display = 'block';
          resource.element.style.order = index;
          
          // Add stagger animation
          resource.element.style.animationDelay = `${index * 50}ms`;
          resource.element.classList.add('animate-fade-in-up');
        });
      }
    }
    
    toggleView() {
      const container = document.getElementById('resources-container');
      const viewToggle = document.getElementById('view-toggle');
      
      if (!container || !viewToggle) return;
      
      this.isGridView = !this.isGridView;
      
      if (this.isGridView) {
        container.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
        viewToggle.innerHTML = `
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
          </svg>
          Grid
        `;
      } else {
        container.className = 'space-y-4';
        viewToggle.innerHTML = `
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          List
        `;
      }
    }
    
    toggleBookmark(btn) {
      const isBookmarked = btn.classList.contains('bookmarked');
      const svg = btn.querySelector('svg');
      
      if (isBookmarked) {
        btn.classList.remove('bookmarked', 'text-amber-500');
        btn.classList.add('text-slate-400');
        svg.setAttribute('fill', 'none');
        this.showToast('Bookmark removed');
      } else {
        btn.classList.add('bookmarked', 'text-amber-500');
        btn.classList.remove('text-slate-400');
        svg.setAttribute('fill', 'currentColor');
        this.showToast('Bookmarked!');
      }
      
      // Save to localStorage
      this.saveBookmarks();
    }
    
    shareResource(btn) {
      const url = btn.dataset.url;
      const title = btn.dataset.title;
      
      if (navigator.share) {
        navigator.share({
          title: title,
          url: url
        }).catch(err => console.log('Error sharing:', err));
      } else if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          this.showToast('Link copied to clipboard!');
        });
      } else {
        this.showToast('Link: ' + url);
      }
    }
    
    initializeBookmarks() {
      const bookmarks = JSON.parse(localStorage.getItem('resourceBookmarks') || '[]');
      bookmarks.forEach(url => {
        const btn = document.querySelector(`[data-url="${url}"]`);
        if (btn) {
          btn.classList.add('bookmarked', 'text-amber-500');
          btn.classList.remove('text-slate-400');
          btn.querySelector('svg').setAttribute('fill', 'currentColor');
        }
      });
    }
    
    saveBookmarks() {
      const bookmarkedUrls = Array.from(document.querySelectorAll('.bookmark-btn.bookmarked'))
        .map(btn => btn.dataset.url);
      localStorage.setItem('resourceBookmarks', JSON.stringify(bookmarkedUrls));
    }
    
    showToast(message) {
      const toast = document.createElement('div');
      toast.className = 'fixed bottom-4 right-4 bg-slate-800 text-white px-6 py-3 rounded-xl shadow-lg z-50 transform translate-y-full opacity-0 transition-all duration-300';
      toast.textContent = message;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.classList.remove('translate-y-full', 'opacity-0');
      }, 100);
      
      setTimeout(() => {
        toast.classList.add('translate-y-full', 'opacity-0');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }
    
    async loadRSSFeed() {
      const preview = document.getElementById('rss-feed-preview');
      if (!preview) return;
      
      try {
        setTimeout(() => {
          preview.innerHTML = `
            <div class="space-y-4">
              <div class="border-l-4 border-indigo-500 pl-4">
                <h4 class="font-semibold text-slate-800 dark:text-slate-200 text-sm mb-1">
                  Building Modern Web Applications with Astro
                </h4>
                <p class="text-slate-600 dark:text-slate-400 text-xs">2 days ago</p>
              </div>
              <div class="border-l-4 border-emerald-500 pl-4">
                <h4 class="font-semibold text-slate-800 dark:text-slate-200 text-sm mb-1">
                  TypeScript Best Practices for 2024
                </h4>
                <p class="text-slate-600 dark:text-slate-400 text-xs">1 week ago</p>
              </div>
              <div class="border-l-4 border-purple-500 pl-4">
                <h4 class="font-semibold text-slate-800 dark:text-slate-200 text-sm mb-1">
                  Performance Optimization Techniques
                </h4>
                <p class="text-slate-600 dark:text-slate-400 text-xs">2 weeks ago</p>
              </div>
            </div>
          `;
        }, 1500);
      } catch (error) {
        preview.innerHTML = '<p class="text-slate-500 text-sm">Unable to load RSS feed</p>';
      }
    }
  }
  
  // RSS Modal Functions
  function showRSSOptions() {
    const modal = document.getElementById('rss-modal');
    if (modal) {
      modal.classList.remove('hidden');
      modal.setAttribute('aria-hidden', 'false');
      document.body.style.overflow = 'hidden';
    }
  }

  function closeRSSModal() {
    const modal = document.getElementById('rss-modal');
    if (modal) {
      modal.classList.add('hidden');
      modal.setAttribute('aria-hidden', 'true');
      document.body.style.overflow = '';
    }
  }

  function copyRSSUrl() {
    const url = 'https://nobhokleng.dev/rss.xml';
    if (navigator.clipboard) {
      navigator.clipboard.writeText(url).then(() => {
        showToast('RSS URL copied to clipboard!');
      });
    } else {
      showToast('Please copy this URL manually: ' + url);
    }
  }

  function copyFromInput() {
    const input = document.getElementById('rss-url-input');
    if (input && input instanceof HTMLInputElement) {
      input.select();
      if (navigator.clipboard) {
        navigator.clipboard.writeText(input.value).then(() => {
          showToast('RSS URL copied to clipboard!');
        });
      } else {
        try {
          document.execCommand('copy');
          showToast('RSS URL copied to clipboard!');
        } catch (err) {
          showToast('Please copy this URL manually: ' + input.value);
        }
      }
    }
  }
  
  function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 bg-slate-800 text-white px-6 py-3 rounded-xl shadow-lg z-50 transform translate-y-full opacity-0 transition-all duration-300';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.classList.remove('translate-y-full', 'opacity-0');
    }, 100);
    
    setTimeout(() => {
      toast.classList.add('translate-y-full', 'opacity-0');
      setTimeout(() => toast.remove(), 300);
    }, 3000);
  }

  // Close modal when clicking outside
  document.addEventListener('click', (e) => {
    const modal = document.getElementById('rss-modal');
    if (e.target === modal) {
      closeRSSModal();
    }
  });
  
  // Keyboard navigation
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      closeRSSModal();
    }
  });

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new ResourcesManager();
    });
  } else {
    new ResourcesManager();
  }
  
  // Re-initialize on page navigation (for Astro)
  document.addEventListener('astro:page-load', () => {
    new ResourcesManager();
  });

  // Make functions available globally
  window.showRSSOptions = showRSSOptions;
  window.closeRSSModal = closeRSSModal;
  window.copyRSSUrl = copyRSSUrl;
  window.copyFromInput = copyFromInput;
</script>