---
// Modern Theme Toggle Switch v3.0 - 2025 Design
// Toggle switch with glassmorphism and smooth animations
---

<button
  id="theme-toggle"
  class="theme-toggle"
  data-theme="light"
  aria-label="Toggle between light and dark theme"
  title="Switch theme"
  role="switch"
  aria-checked="false"
>
  <!-- Toggle Track -->
  <div class="toggle-track">
    <!-- Toggle Thumb -->
    <div class="toggle-thumb">
      <!-- Sun Icon -->
      <svg class="icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="4"/>
        <path d="M12 3v1M12 20v1M21 12h-1M4 12H3M18.364 5.636l-.707.707M6.343 17.657l-.707.707M5.636 5.636l.707.707M17.657 17.657l.707.707"/>
      </svg>
      <!-- <PERSON> Icon -->
      <svg class="icon moon-icon" viewBox="0 0 24 24" fill="currentColor">
        <path d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z"/>
      </svg>
    </div>
  </div>
</button>

<script>
  // Modern Theme Toggle Switch v3.0
  function initThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle') as HTMLButtonElement;
    const html = document.documentElement;

    if (!themeToggle) return;

    // Initialize theme from localStorage or system preference
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const shouldUseDark = savedTheme === 'dark' || (!savedTheme && systemPrefersDark);

    // Apply initial theme
    html.classList.toggle('dark', shouldUseDark);
    themeToggle.setAttribute('aria-checked', shouldUseDark.toString());
    themeToggle.setAttribute('data-theme', shouldUseDark ? 'dark' : 'light');
    
    // Force a reflow to ensure initial state is applied
    themeToggle.offsetHeight;
    
    // Add haptic feedback support for mobile
    const supportsHaptic = 'vibrate' in navigator;

    // Simple and reliable toggle function
    function toggleTheme(event: Event) {
      const isDark = html.classList.contains('dark');
      const newTheme = isDark ? 'light' : 'dark';

      // Apply theme change
      html.classList.toggle('dark', !isDark);
      localStorage.setItem('theme', newTheme);
      themeToggle.setAttribute('aria-checked', (!isDark).toString());
      themeToggle.setAttribute('data-theme', newTheme);

      // Announce change for screen readers
      announceThemeChange(newTheme + ' mode');

      // Dispatch event for other components
      window.dispatchEvent(new CustomEvent('theme-changed', {
        detail: {
          theme: newTheme,
          source: 'user',
          timestamp: Date.now()
        }
      }));
    }


    // Announce theme changes to screen readers
    function announceThemeChange(theme: string) {
      const announcement = document.createElement('div');
      announcement.setAttribute('aria-live', 'polite');
      announcement.className = 'sr-only';
      announcement.textContent = `Switched to ${theme}`;
      document.body.appendChild(announcement);

      setTimeout(() => {
        document.body.removeChild(announcement);
      }, 1000);
    }

    // Simple event listeners
    themeToggle.addEventListener('click', toggleTheme);
    
    // Keyboard support
    themeToggle.addEventListener('keydown', (event: KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        toggleTheme(event);
      }
    });

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme) {
        html.classList.toggle('dark', e.matches);
        themeToggle.setAttribute('aria-checked', e.matches.toString());
        themeToggle.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        
        window.dispatchEvent(new CustomEvent('theme-changed', {
          detail: {
            theme: e.matches ? 'dark' : 'light',
            source: 'system',
            timestamp: Date.now()
          }
        }));
      }
    });
  }

  // Initialize with performance optimization
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initThemeToggle);
  } else {
    // Use requestIdleCallback for better performance
    if ('requestIdleCallback' in window) {
      requestIdleCallback(initThemeToggle);
    } else {
      setTimeout(initThemeToggle, 0);
    }
  }

  // Re-initialize on Astro page navigation
  document.addEventListener('astro:page-load', () => {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(initThemeToggle);
    } else {
      setTimeout(initThemeToggle, 0);
    }
  });
</script>

<style>
  /* Fixed Modern Theme Toggle */
  .theme-toggle {
    position: relative;
    width: 60px;
    height: 30px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    outline: none;
    background: #e5e7eb;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    padding: 2px;
  }

  .theme-toggle[data-theme="dark"] {
    background: #374151;
  }

  .dark .theme-toggle {
    background: #374151;
  }

  /* Toggle Track */
  .toggle-track {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  /* Toggle Thumb */
  .toggle-thumb {
    position: relative;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), background 0.3s ease;
    transform: translateX(0);
  }

  /* Theme specific styles */
  .theme-toggle[data-theme="dark"] .toggle-thumb {
    background: #1f2937;
    transform: translateX(30px);
  }

  .dark .toggle-thumb {
    background: #1f2937;
  }

  /* Icons */
  .icon {
    width: 14px;
    height: 14px;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s ease;
    position: absolute;
  }

  .sun-icon {
    color: #f59e0b;
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }

  .moon-icon {
    color: #6366f1;
    opacity: 0;
    transform: scale(0.5) rotate(90deg);
  }

  /* Theme specific icon states */
  .theme-toggle[data-theme="dark"] .sun-icon {
    opacity: 0;
    transform: scale(0.5) rotate(-90deg);
  }

  .theme-toggle[data-theme="dark"] .moon-icon {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }


  /* Hover Effects */
  .theme-toggle:hover {
    transform: scale(1.05);
  }

  .theme-toggle:hover .toggle-thumb {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  /* Active State */
  .theme-toggle:active {
    transform: scale(0.95);
  }

  /* Focus State */
  .theme-toggle:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px #3b82f6;
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    .theme-toggle,
    .toggle-thumb,
    .icon {
      transition: none !important;
    }
  }


  /* Screen reader only */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .theme-toggle {
      width: 54px;
      height: 28px;
    }
    
    .toggle-thumb {
      width: 24px;
      height: 24px;
    }
    
    .theme-toggle[data-theme="dark"] .toggle-thumb {
      transform: translateX(26px);
    }
    
    .dark .toggle-thumb {
      background: #1f2937;
    }
    
    .icon {
      width: 12px;
      height: 12px;
    }
  }
</style>
