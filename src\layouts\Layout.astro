---
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import PerformanceMonitor from '../components/PerformanceMonitor.astro';
import ScrollAnimations from '../components/ScrollAnimations.astro';
import '../styles/global.css';

interface Props {
  title?: string;
  description?: string;
  ogImage?: string;
}

const { 
  title = 'Nob Hokleng | Software Developer & System Architect',
  description = 'Software developer with experience in scalable systems. Building high-performance applications with modern architecture patterns and best practices.',
  ogImage = 'https://nobhokleng.dev/images/og-image.jpg'
} = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>{title}</title>
    <meta name="title" content={title}>
    <meta name="description" content={description}>
    <meta name="keywords" content="software developer, backend developer, system architecture, scalable systems, web development, programming">
    <meta name="author" content="Nob Hokleng">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nobhokleng.dev/">
    <meta property="og:title" content={title}>
    <meta property="og:description" content={description}>
    <meta property="og:image" content={ogImage}>

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://nobhokleng.dev/">
    <meta property="twitter:title" content={title}>
    <meta property="twitter:description" content={description}>
    <meta property="twitter:image" content={ogImage}>

    <!-- Canonical URL -->
    <link rel="canonical" href="https://nobhokleng.dev/">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦕</text></svg>">

    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="NobHokleng">
    <meta name="theme-color" content="#ff9e00">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="https://github.com">
    <link rel="dns-prefetch" href="https://linkedin.com">

    <!-- Optimized font loading with preconnect and font-display -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <!-- Critical fonts with font-display: swap for better performance -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap"></noscript>

    <!-- Font Awesome with optimized loading -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"></noscript>
    <noscript><link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family:Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet"></noscript>

    <!-- Structured Data -->
    <script type="application/ld+json" is:inline>
    {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Nob Hokleng",
        "jobTitle": "Software Developer",
        "description": "Software developer with experience in scalable systems and modern architecture patterns",
        "url": "https://nobhokleng.dev",
        "sameAs": [
            "https://github.com/Nobhokleng",
            "https://linkedin.com/in/nobhokleng"
        ],
        "knowsAbout": [
            "Software Development",
            "System Architecture",
            "Backend Development",
            "DevOps",
            "Scalable Systems"
        ]
    }
    </script>
    <!-- Theme initialization script (must be in head to prevent FOUC) -->
    <script is:inline>
      // Initialize theme before page renders to prevent flash
      (function() {
        const savedTheme = localStorage.getItem('theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      })();
    </script>
  </head>
  <body class="bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark transition-colors duration-300 min-h-screen flex flex-col">
    <Header />
    <main class="flex-grow">
      <slot />
    </main>
    <Footer />

    <!-- Performance monitoring for Core Web Vitals -->
    <PerformanceMonitor />

    <!-- Scroll animations and micro-interactions -->
    <ScrollAnimations />


    <!-- Service Worker Registration -->
    <script is:inline>
      // Register service worker for caching and offline functionality
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('Service Worker registered successfully:', registration.scope);
            })
            .catch((error) => {
              console.log('Service Worker registration failed:', error);
            });
        });
      }
    </script>
  </body>
</html> 